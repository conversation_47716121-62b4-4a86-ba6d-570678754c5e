using Alpaca.Markets;

class Program
{
    static async Task Main(string[] args)
    {
        try
        {
            Console.WriteLine("🧪 Direct Alpaca Paper Trade Test");
            Console.WriteLine();

            // Paper trading credentials from your config
            var keyId = "PK0AM3WB1CES3YBQPGR0";
            var secretKey = "2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf";

            Console.WriteLine($"🔑 Using Paper Trading Credentials");
            Console.WriteLine($"📊 Key ID: {keyId}");
            Console.WriteLine();

            // Create Alpaca client for paper trading
            var config = new AlpacaTradingClientConfiguration()
            {
                ApiEndpoint = Environments.Paper.AlpacaTradingApi,
                SecurityId = new SecretKey(keyId, secretKey),
                HttpClient = new HttpClient()
            };

            using var tradingClient = Environments.Paper.GetAlpacaTradingClient(config);

            Console.WriteLine("🔗 Testing Alpaca connection...");
            
            // Test account access
            var account = await tradingClient.GetAccountAsync();
            Console.WriteLine($"✅ Account connected: {account.AccountId}");
            Console.WriteLine($"💰 Buying Power: ${account.BuyingPower:F2}");
            Console.WriteLine($"📈 Portfolio Value: ${account.PortfolioValue:F2}");
            Console.WriteLine($"🏦 Account Status: {account.Status}");
            Console.WriteLine();

            // Check current positions
            var positions = await tradingClient.ListPositionsAsync();
            Console.WriteLine($"📊 Current Positions: {positions.Count}");
            foreach (var position in positions.Take(3))
            {
                Console.WriteLine($"   {position.Symbol}: {position.Quantity} shares @ ${position.AverageEntryPrice:F2}");
            }
            Console.WriteLine();

            // Execute a small test trade
            Console.WriteLine("🚀 Executing test paper trade...");
            Console.WriteLine("📤 Submitting order: BUY 1 share of AAPL (Market Order)");

            var orderRequest = MarketOrderRequest.Buy("AAPL", new Quantity(1));
            
            var order = await tradingClient.PostOrderAsync(orderRequest);
            
            Console.WriteLine($"✅ Paper trade order submitted successfully!");
            Console.WriteLine($"   Order ID: {order.OrderId}");
            Console.WriteLine($"   Symbol: {order.Symbol}");
            Console.WriteLine($"   Quantity: {order.Quantity} shares");
            Console.WriteLine($"   Side: {order.OrderSide}");
            Console.WriteLine($"   Type: {order.OrderType}");
            Console.WriteLine($"   Status: {order.OrderStatus}");
            Console.WriteLine($"   Submitted At: {order.SubmittedAtUtc:yyyy-MM-dd HH:mm:ss} UTC");
            Console.WriteLine();

            // Wait and check order status
            Console.WriteLine("⏳ Waiting 5 seconds to check order status...");
            await Task.Delay(5000);
            
            var updatedOrder = await tradingClient.GetOrderAsync(order.OrderId);
            Console.WriteLine($"📊 Updated Order Status: {updatedOrder.OrderStatus}");
            
            if (updatedOrder.FilledQuantity > 0)
            {
                Console.WriteLine($"✅ Order filled: {updatedOrder.FilledQuantity} shares @ ${updatedOrder.AverageFillPrice:F2}");
                Console.WriteLine($"💰 Total Value: ${updatedOrder.FilledQuantity * updatedOrder.AverageFillPrice:F2}");
            }
            else if (updatedOrder.OrderStatus == OrderStatus.Pending)
            {
                Console.WriteLine("⏳ Order still pending - this is normal for paper trading");
            }
            
            Console.WriteLine();
            Console.WriteLine("✅ Direct paper trade test completed successfully!");
            Console.WriteLine("🎯 This proves the basic trading infrastructure works!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error in direct trade test: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"   Inner: {ex.InnerException.Message}");
            }
        }
    }
}
