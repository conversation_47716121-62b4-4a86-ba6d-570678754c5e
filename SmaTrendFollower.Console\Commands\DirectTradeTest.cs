using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Alpaca.Markets;
using System.Net;

namespace SmaTrendFollower.Commands;

/// <summary>
/// Direct Alpaca trading test that bypasses all complex dependencies
/// </summary>
public static class DirectTradeTest
{
    public static async Task ExecuteDirectPaperTradeAsync()
    {
        try
        {
            Console.WriteLine("🧪 Direct Paper Trade Test - Bypassing all complex services");
            Console.WriteLine();

            // Load configuration directly
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("SmaTrendFollower.Console/appsettings.json", optional: true)
                .AddJsonFile("SmaTrendFollower.Console/appsettings.LocalProd.json", optional: true)
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile("appsettings.LocalProd.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            // Get Alpaca credentials
            var keyId = configuration["Alpaca:KeyId"];
            var secretKey = configuration["Alpaca:SecretKey"];
            var environment = configuration["Alpaca:Environment"];

            Console.WriteLine($"📊 Using Alpaca {environment} environment");
            Console.WriteLine($"🔑 Key ID: {keyId}");
            Console.WriteLine();

            if (string.IsNullOrEmpty(keyId) || string.IsNullOrEmpty(secretKey))
            {
                Console.WriteLine("❌ Alpaca credentials not found in configuration");
                return;
            }

            // Create Alpaca client directly
            var alpacaEnvironment = environment?.ToLower() == "live" 
                ? Environments.Live 
                : Environments.Paper;

            var config = new AlpacaTradingClientConfiguration()
            {
                ApiEndpoint = alpacaEnvironment.AlpacaTradingApi,
                SecurityId = new SecretKey(keyId, secretKey),
                HttpClient = new HttpClient()
            };

            using var tradingClient = alpacaEnvironment.GetAlpacaTradingClient(config);

            Console.WriteLine("🔗 Testing Alpaca connection...");
            
            // Test account access
            var account = await tradingClient.GetAccountAsync();
            Console.WriteLine($"✅ Account connected: {account.AccountId}");
            Console.WriteLine($"💰 Buying Power: ${account.BuyingPower:F2}");
            Console.WriteLine($"📈 Portfolio Value: ${account.PortfolioValue:F2}");
            Console.WriteLine($"🏦 Account Status: {account.Status}");
            Console.WriteLine($"🌍 Environment: {(account.IsDayTrader ? "Day Trader" : "Regular")} Account");
            Console.WriteLine();

            // Check current positions
            var positions = await tradingClient.ListPositionsAsync();
            Console.WriteLine($"📊 Current Positions: {positions.Count}");
            foreach (var position in positions.Take(5))
            {
                Console.WriteLine($"   {position.Symbol}: {position.Quantity} shares @ ${position.AverageEntryPrice:F2}");
            }
            Console.WriteLine();

            // Test market data access
            Console.WriteLine("📈 Testing market data access...");
            try
            {
                var dataConfig = new AlpacaDataClientConfiguration()
                {
                    ApiEndpoint = alpacaEnvironment.AlpacaDataApi,
                    SecurityId = new SecretKey(keyId, secretKey),
                    HttpClient = new HttpClient()
                };

                using var dataClient = alpacaEnvironment.GetAlpacaDataClient(dataConfig);
                
                var quote = await dataClient.GetLatestQuoteAsync(new LatestMarketDataRequest("AAPL"));
                if (quote != null)
                {
                    Console.WriteLine($"✅ AAPL Quote: Bid=${quote.Bid:F2}, Ask=${quote.Ask:F2}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Market data test failed: {ex.Message}");
            }
            Console.WriteLine();

            // Execute a small test trade
            Console.WriteLine("🚀 Executing test paper trade...");
            Console.WriteLine("📤 Submitting order: BUY 1 share of AAPL (Market Order)");

            var orderRequest = MarketOrderRequest.Buy("AAPL", new Quantity(1));
            
            var order = await tradingClient.PostOrderAsync(orderRequest);
            
            Console.WriteLine($"✅ Paper trade order submitted successfully!");
            Console.WriteLine($"   Order ID: {order.OrderId}");
            Console.WriteLine($"   Symbol: {order.Symbol}");
            Console.WriteLine($"   Quantity: {order.Quantity} shares");
            Console.WriteLine($"   Side: {order.OrderSide}");
            Console.WriteLine($"   Type: {order.OrderType}");
            Console.WriteLine($"   Status: {order.OrderStatus}");
            Console.WriteLine($"   Submitted At: {order.SubmittedAtUtc:yyyy-MM-dd HH:mm:ss} UTC");
            Console.WriteLine();

            // Wait and check order status
            Console.WriteLine("⏳ Waiting 5 seconds to check order status...");
            await Task.Delay(5000);
            
            var updatedOrder = await tradingClient.GetOrderAsync(order.OrderId);
            Console.WriteLine($"📊 Updated Order Status: {updatedOrder.OrderStatus}");
            
            if (updatedOrder.FilledQuantity > 0)
            {
                Console.WriteLine($"✅ Order filled: {updatedOrder.FilledQuantity} shares @ ${updatedOrder.AverageFillPrice:F2}");
                Console.WriteLine($"💰 Total Value: ${updatedOrder.FilledQuantity * updatedOrder.AverageFillPrice:F2}");
            }
            else if (updatedOrder.OrderStatus == OrderStatus.Pending)
            {
                Console.WriteLine("⏳ Order still pending - this is normal for paper trading");
            }
            
            Console.WriteLine();
            Console.WriteLine("✅ Direct paper trade test completed successfully!");
            Console.WriteLine("🎯 This proves the basic trading infrastructure works!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error in direct trade test: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"   Inner: {ex.InnerException.Message}");
            }
            Console.WriteLine($"🔍 Stack trace: {ex.StackTrace}");
        }
    }
}
