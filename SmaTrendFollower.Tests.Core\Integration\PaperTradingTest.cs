using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Xunit;
using Xunit.Abstractions;
using Alpaca.Markets;

namespace SmaTrendFollower.Tests.Core.Integration;

public class PaperTradingTest
{
    private readonly ITestOutputHelper _output;

    public PaperTradingTest(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact]
    public async Task CanConnectToPaperTradingAccount()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string>
            {
                ["Alpaca:KeyId"] = "PK0AM3WB1CES3YBQPGR0",
                ["Alpaca:SecretKey"] = "2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf",
                ["Alpaca:Environment"] = "paper"
            })
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder => builder.AddXUnit(_output));
        
        // Add minimal Alpaca services
        services.AddSingleton<IAlpacaClientFactory>(provider =>
        {
            var config = provider.GetRequiredService<IConfiguration>();
            var keyId = config["Alpaca:KeyId"];
            var secretKey = config["Alpaca:SecretKey"];
            var environment = config["Alpaca:Environment"];
            
            return new AlpacaClientFactory(keyId, secretKey, environment == "live");
        });

        var serviceProvider = services.BuildServiceProvider();

        // Act & Assert
        var factory = serviceProvider.GetRequiredService<IAlpacaClientFactory>();
        var tradingClient = factory.GetTradingClient();
        
        _output.WriteLine("Testing Alpaca paper trading connection...");
        
        var account = await tradingClient.GetAccountAsync();
        
        _output.WriteLine($"✅ Account connected: {account.AccountId}");
        _output.WriteLine($"💰 Buying Power: ${account.BuyingPower:F2}");
        _output.WriteLine($"📈 Portfolio Value: ${account.PortfolioValue:F2}");
        _output.WriteLine($"🏦 Account Status: {account.Status}");
        
        Assert.NotNull(account);
        Assert.NotNull(account.AccountId);
        Assert.True(account.BuyingPower > 0);
    }

    [Fact]
    public async Task CanSubmitPaperTradeOrder()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string>
            {
                ["Alpaca:KeyId"] = "PK0AM3WB1CES3YBQPGR0",
                ["Alpaca:SecretKey"] = "2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf",
                ["Alpaca:Environment"] = "paper"
            })
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder => builder.AddXUnit(_output));
        
        // Add minimal Alpaca services
        services.AddSingleton<IAlpacaClientFactory>(provider =>
        {
            var config = provider.GetRequiredService<IConfiguration>();
            var keyId = config["Alpaca:KeyId"];
            var secretKey = config["Alpaca:SecretKey"];
            var environment = config["Alpaca:Environment"];
            
            return new AlpacaClientFactory(keyId, secretKey, environment == "live");
        });

        var serviceProvider = services.BuildServiceProvider();

        // Act
        var factory = serviceProvider.GetRequiredService<IAlpacaClientFactory>();
        var tradingClient = factory.GetTradingClient();
        
        _output.WriteLine("Submitting paper trade order...");
        
        var orderRequest = MarketOrderRequest.Buy("AAPL", new Quantity(1));
        var order = await tradingClient.PostOrderAsync(orderRequest);
        
        _output.WriteLine($"✅ Paper trade order submitted successfully!");
        _output.WriteLine($"   Order ID: {order.OrderId}");
        _output.WriteLine($"   Symbol: {order.Symbol}");
        _output.WriteLine($"   Quantity: {order.Quantity} shares");
        _output.WriteLine($"   Side: {order.OrderSide}");
        _output.WriteLine($"   Type: {order.OrderType}");
        _output.WriteLine($"   Status: {order.OrderStatus}");
        
        // Wait and check order status
        _output.WriteLine("⏳ Waiting 3 seconds to check order status...");
        await Task.Delay(3000);
        
        var updatedOrder = await tradingClient.GetOrderAsync(order.OrderId);
        _output.WriteLine($"📊 Updated Order Status: {updatedOrder.OrderStatus}");
        
        if (updatedOrder.FilledQuantity > 0)
        {
            _output.WriteLine($"✅ Order filled: {updatedOrder.FilledQuantity} shares @ ${updatedOrder.AverageFillPrice:F2}");
            _output.WriteLine($"💰 Total Value: ${updatedOrder.FilledQuantity * updatedOrder.AverageFillPrice:F2}");
        }
        else
        {
            _output.WriteLine("⏳ Order still pending - this is normal for paper trading");
        }

        // Assert
        Assert.NotNull(order);
        Assert.NotNull(order.OrderId);
        Assert.Equal("AAPL", order.Symbol);
        Assert.Equal(OrderSide.Buy, order.OrderSide);
        Assert.Equal(OrderType.Market, order.OrderType);
    }
}

// Simple AlpacaClientFactory for testing
public class AlpacaClientFactory : IAlpacaClientFactory
{
    private readonly string _keyId;
    private readonly string _secretKey;
    private readonly bool _isLive;

    public AlpacaClientFactory(string keyId, string secretKey, bool isLive)
    {
        _keyId = keyId;
        _secretKey = secretKey;
        _isLive = isLive;
    }

    public IAlpacaTradingClient GetTradingClient()
    {
        var environment = _isLive ? Environments.Live : Environments.Paper;
        var config = new AlpacaTradingClientConfiguration()
        {
            ApiEndpoint = environment.AlpacaTradingApi,
            SecurityId = new SecretKey(_keyId, _secretKey),
            HttpClient = new HttpClient()
        };
        return environment.GetAlpacaTradingClient(config);
    }

    public IAlpacaDataClient GetDataClient()
    {
        var environment = _isLive ? Environments.Live : Environments.Paper;
        var config = new AlpacaDataClientConfiguration()
        {
            ApiEndpoint = environment.AlpacaDataApi,
            SecurityId = new SecretKey(_keyId, _secretKey),
            HttpClient = new HttpClient()
        };
        return environment.GetAlpacaDataClient(config);
    }

    public IAlpacaCryptoDataClient GetCryptoDataClient()
    {
        throw new NotImplementedException();
    }

    public IAlpacaNewsStreamingClient GetNewsStreamingClient()
    {
        throw new NotImplementedException();
    }

    public IAlpacaStreamingClient GetStreamingClient()
    {
        throw new NotImplementedException();
    }

    public IAlpacaCryptoStreamingClient GetCryptoStreamingClient()
    {
        throw new NotImplementedException();
    }
}
