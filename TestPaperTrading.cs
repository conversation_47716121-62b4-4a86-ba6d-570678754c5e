using System;
using System.Threading.Tasks;
using Alpaca.Markets;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🧪 Direct Paper Trading Test");
        Console.WriteLine("=" * 50);

        try
        {
            // Force paper trading environment
            Environment.SetEnvironmentVariable("APCA_API_ENV", "paper");
            
            // Paper trading credentials
            var keyId = "PK0AM3WB1CES3YBQPGR0";
            var secretKey = "2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf";
            
            Console.WriteLine($"📊 Using paper trading credentials: {keyId}");
            
            // Create Alpaca client for paper trading
            var alpacaEnvironment = Environments.Paper;
            var tradingClient = alpacaEnvironment.GetAlpacaTradingClient(new SecretKey(keyId, secretKey));
            
            // Test 1: Get account info
            Console.WriteLine("🔍 Testing account access...");
            var account = await tradingClient.GetAccountAsync();
            
            Console.WriteLine("✅ Paper account connected successfully!");
            Console.WriteLine($"   Account ID: {account.AccountId}");
            Console.WriteLine($"   Buying Power: ${account.BuyingPower:F2}");
            Console.WriteLine($"   Portfolio Value: ${account.PortfolioValue:F2}");
            Console.WriteLine($"   Account Status: {account.Status}");
            
            // Test 2: Submit a paper trade
            Console.WriteLine("🚀 Submitting paper trade order...");
            var orderRequest = MarketOrderRequest.Buy("AAPL", new Quantity(1));
            var order = await tradingClient.PostOrderAsync(orderRequest);
            
            Console.WriteLine("✅ Paper trade order submitted successfully!");
            Console.WriteLine($"   Order ID: {order.OrderId}");
            Console.WriteLine($"   Symbol: {order.Symbol}");
            Console.WriteLine($"   Quantity: {order.Quantity} shares");
            Console.WriteLine($"   Side: {order.OrderSide}");
            Console.WriteLine($"   Type: {order.OrderType}");
            Console.WriteLine($"   Status: {order.OrderStatus}");
            Console.WriteLine($"   Submitted At: {order.SubmittedAtUtc}");
            
            // Wait and check order status
            Console.WriteLine("⏳ Waiting 3 seconds to check order status...");
            await Task.Delay(3000);
            
            var updatedOrder = await tradingClient.GetOrderAsync(order.OrderId);
            Console.WriteLine($"📊 Updated Order Status: {updatedOrder.OrderStatus}");
            
            if (updatedOrder.FilledQuantity > 0)
            {
                Console.WriteLine($"✅ Order filled: {updatedOrder.FilledQuantity} shares @ ${updatedOrder.AverageFillPrice:F2}");
                Console.WriteLine($"💰 Total Value: ${updatedOrder.FilledQuantity * updatedOrder.AverageFillPrice:F2}");
            }
            else
            {
                Console.WriteLine("⏳ Order still pending - this is normal for paper trading");
            }
            
            Console.WriteLine();
            Console.WriteLine("🎉 PAPER TRADING TEST COMPLETED SUCCESSFULLY!");
            Console.WriteLine("🎯 The Alpaca paper trading API is working correctly!");
            Console.WriteLine("🚀 SmaTrendFollower can now execute paper trades!");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error in paper trading test: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        
        Console.WriteLine();
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
