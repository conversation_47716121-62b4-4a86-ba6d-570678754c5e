using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🧪 Direct Alpaca Paper Trading Test");
        Console.WriteLine("=" * 50);

        // Paper trading credentials
        var apiKey = "PK0AM3WB1CES3YBQPGR0";
        var secretKey = "2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf";
        var baseUrl = "https://paper-api.alpaca.markets";

        using var httpClient = new HttpClient();
        httpClient.DefaultRequestHeaders.Add("APCA-API-KEY-ID", apiKey);
        httpClient.DefaultRequestHeaders.Add("APCA-API-SECRET-KEY", secretKey);

        try
        {
            // Test 1: Get account info
            Console.WriteLine("📊 Testing account access...");
            var accountResponse = await httpClient.GetAsync($"{baseUrl}/v2/account");
            
            if (accountResponse.IsSuccessStatusCode)
            {
                var accountJson = await accountResponse.Content.ReadAsStringAsync();
                var account = JsonSerializer.Deserialize<JsonElement>(accountJson);
                
                Console.WriteLine($"✅ Account connected: {account.GetProperty("id").GetString()}");
                Console.WriteLine($"💰 Buying Power: ${account.GetProperty("buying_power").GetDecimal():F2}");
                Console.WriteLine($"📈 Portfolio Value: ${account.GetProperty("portfolio_value").GetDecimal():F2}");
                Console.WriteLine($"🏦 Account Status: {account.GetProperty("status").GetString()}");
            }
            else
            {
                Console.WriteLine($"❌ Account access failed: {accountResponse.StatusCode}");
                var error = await accountResponse.Content.ReadAsStringAsync();
                Console.WriteLine($"Error: {error}");
                return;
            }

            Console.WriteLine();

            // Test 2: Submit a test order
            Console.WriteLine("🚀 Testing order submission...");
            var orderData = new
            {
                symbol = "AAPL",
                qty = 1,
                side = "buy",
                type = "market",
                time_in_force = "day"
            };

            var orderJson = JsonSerializer.Serialize(orderData);
            var orderContent = new StringContent(orderJson, Encoding.UTF8, "application/json");
            
            var orderResponse = await httpClient.PostAsync($"{baseUrl}/v2/orders", orderContent);
            
            if (orderResponse.IsSuccessStatusCode)
            {
                var orderResponseJson = await orderResponse.Content.ReadAsStringAsync();
                var order = JsonSerializer.Deserialize<JsonElement>(orderResponseJson);
                
                Console.WriteLine($"✅ Paper trade order submitted successfully!");
                Console.WriteLine($"   Order ID: {order.GetProperty("id").GetString()}");
                Console.WriteLine($"   Symbol: {order.GetProperty("symbol").GetString()}");
                Console.WriteLine($"   Quantity: {order.GetProperty("qty").GetString()} shares");
                Console.WriteLine($"   Side: {order.GetProperty("side").GetString()}");
                Console.WriteLine($"   Type: {order.GetProperty("type").GetString()}");
                Console.WriteLine($"   Status: {order.GetProperty("status").GetString()}");
                Console.WriteLine($"   Submitted At: {order.GetProperty("submitted_at").GetString()}");
                
                // Wait and check order status
                Console.WriteLine();
                Console.WriteLine("⏳ Waiting 3 seconds to check order status...");
                await Task.Delay(3000);
                
                var statusResponse = await httpClient.GetAsync($"{baseUrl}/v2/orders/{order.GetProperty("id").GetString()}");
                if (statusResponse.IsSuccessStatusCode)
                {
                    var statusJson = await statusResponse.Content.ReadAsStringAsync();
                    var updatedOrder = JsonSerializer.Deserialize<JsonElement>(statusJson);
                    Console.WriteLine($"📊 Updated Order Status: {updatedOrder.GetProperty("status").GetString()}");
                    
                    if (updatedOrder.TryGetProperty("filled_qty", out var filledQtyProp) && 
                        filledQtyProp.GetString() != "0")
                    {
                        var filledQty = decimal.Parse(filledQtyProp.GetString());
                        var avgPrice = decimal.Parse(updatedOrder.GetProperty("filled_avg_price").GetString());
                        Console.WriteLine($"✅ Order filled: {filledQty} shares @ ${avgPrice:F2}");
                        Console.WriteLine($"💰 Total Value: ${filledQty * avgPrice:F2}");
                    }
                    else
                    {
                        Console.WriteLine("⏳ Order still pending - this is normal for paper trading");
                    }
                }
            }
            else
            {
                Console.WriteLine($"❌ Order submission failed: {orderResponse.StatusCode}");
                var error = await orderResponse.Content.ReadAsStringAsync();
                Console.WriteLine($"Error: {error}");
                return;
            }
            
            Console.WriteLine();
            Console.WriteLine("✅ All Alpaca paper trading tests passed!");
            Console.WriteLine("🎯 This proves the basic trading infrastructure works!");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error in Alpaca test: {ex.Message}");
        }
    }
}
